239
backend/common/security/java_adapter.py

普通文件
@ -0,0 +1,239 @@
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统认证适配器
用于处理来自Java系统的token认证
"""
import json
import time
from typing import Dict, Optional

from backend.app.admin.schema.user import GetUserInfoWithRelationDetail
from backend.common.exception import errors
from backend.common.log import log
from backend.database.redis import redis_client


class JavaAdapter:
    """Java系统认证适配器"""
    
    def __init__(self):
        self.enabled = True
        
    def is_enabled(self) -> bool:
        """检查Java适配器是否启用"""
        return self.enabled
    
    async def authenticate_java_token(self, token: str) -> GetUserInfoWithRelationDetail:
        """
        认证Java系统的token

        :param token: Java系统的JWT token
        :return: 用户信息
        """
        try:
            # 1. 解析JWT token获取uuid
            import jwt

	            # Java系统使用的密钥（从配置文件获取，与Java系统保持一致）
            from backend.core.conf import settings
            secret_key = settings.TOKEN_SECRET_KEY

            try:
	                # 方法1：尝试标准JWT验证
                payload = jwt.decode(
                    token,
                    secret_key,
                    algorithms=["HS512"],
                    options={
                        "verify_exp": False,  # Java JWT没有exp字段
                        "verify_iat": False,  # Java JWT没有iat字段
                        "verify_nbf": False   # Java JWT没有nbf字段
                    }
                )
                uuid = payload.get("login_user_key")
                log.info(f"JWT标准验证成功，获取UUID: {uuid}")

            except jwt.InvalidTokenError:
	                # 方法2：兼容性处理 - 直接解析payload（Java JWT库版本差异）
	                log.debug("JWT签名验证失败，使用兼容性解析（Java JWT库版本差异）")

                import base64
                import json

                # 分割token
                parts = token.split('.')
                if len(parts) != 3:
                    raise errors.TokenError(msg='Token 格式错误')

	                # 解析payload部分（不验证签名）
                payload_data = parts[1] + '=' * (4 - len(parts[1]) % 4)  # 补充padding
                payload = json.loads(base64.urlsafe_b64decode(payload_data))
                uuid = payload.get("login_user_key")

                if uuid:
                    log.debug(f"兼容性解析成功，获取UUID: {uuid}")
                else:
                    raise errors.TokenError(msg='Token 格式错误')

            if not uuid:
                log.warning(f"JWT token中没有找到login_user_key")
                raise errors.TokenError(msg='Token 格式错误')

            # 2. 使用uuid从Redis获取用户信息
            redis_key = f"login_tokens:{uuid}"
            cached_data = await redis_client.get(redis_key)

            if not cached_data:
                log.warning(f"Redis中没有找到Java token数据: {redis_key}")
                raise errors.TokenError(msg='Token 无效或已过期')

            # 3. 解析用户数据 (处理Java特有的序列化格式)
            user_data = self._parse_java_json(cached_data)
            
            # 3. 验证token是否过期
            expire_time = user_data.get('expireTime', 0)
            current_time = int(time.time() * 1000)  # 转换为毫秒
            
            if current_time > expire_time:
                log.warning(f"Java token已过期: {expire_time} < {current_time}")
                raise errors.TokenError(msg='Token 已过期')
            
            # 4. 转换Java用户数据为FBA格式
            fba_user = self._convert_java_user_to_fba(user_data)
            
            log.info(f"成功认证Java用户: {user_data.get('username', 'unknown')}")
            return fba_user
            
        except json.JSONDecodeError as e:
            log.warning(f"Java用户数据JSON解析失败: {e}")
            raise errors.TokenError(msg='Token 数据格式错误')
        except Exception as e:
            log.error(f"Java token认证失败: {e}")
            raise errors.TokenError(msg='Token 认证失败')

    def _parse_java_json(self, json_str: str) -> Dict:
        """
        解析Java系统的JSON数据，处理Java特有的序列化格式

        :param json_str: Java系统的JSON字符串
        :return: 解析后的字典
        """
        try:
            # 修复Java特有的Set格式: Set["item1","item2"] -> ["item1","item2"]
            import re

            # 替换 Set[...] 格式为标准数组格式
            fixed_json = re.sub(r'Set\[([^\]]+)\]', r'[\1]', json_str)

            # 替换其他可能的Java集合格式
            # List[...] -> [...]
            fixed_json = re.sub(r'List\[([^\]]+)\]', r'[\1]', fixed_json)

            # HashSet[...] -> [...]
            fixed_json = re.sub(r'HashSet\[([^\]]+)\]', r'[\1]', fixed_json)

            # ArrayList[...] -> [...]
            fixed_json = re.sub(r'ArrayList\[([^\]]+)\]', r'[\1]', fixed_json)

            log.debug(f"修复Java JSON格式: Set[...] -> [...]")

            return json.loads(fixed_json)

        except json.JSONDecodeError as e:
            log.error(f"Java JSON解析失败，即使修复后仍然失败: {e}")
            log.debug(f"修复后的JSON: {fixed_json[:200]}...")
            raise
        except Exception as e:
            log.error(f"Java JSON修复过程失败: {e}")
            raise

    def _convert_java_user_to_fba(self, java_user: Dict) -> GetUserInfoWithRelationDetail:
        """
        将Java用户数据转换为FBA用户格式
        
        :param java_user: Java系统的用户数据
        :return: FBA格式的用户数据
        """
        try:
            # 提取Java用户信息
            user_info = java_user.get('user', {})

            # 处理时间字段
            from datetime import datetime

            def parse_java_time(time_value):
                """解析Java时间戳或时间字符串"""
                if isinstance(time_value, int):
                    # 毫秒时间戳
                    return datetime.fromtimestamp(time_value / 1000)
                elif isinstance(time_value, str):
                    # 时间字符串
                    try:
                        return datetime.strptime(time_value, '%Y-%m-%d %H:%M:%S')
                    except:
                        return datetime.now()
                else:
                    return datetime.now()

            # 构建FBA用户数据
            fba_user_data = {
                'id': user_info.get('userId', 1),
                'uuid': f"java-{user_info.get('userId', 1)}",
                'username': user_info.get('userName', 'admin'),
                'nickname': user_info.get('nickName', '管理员'),
                'email': user_info.get('email', '') or None,  # 空字符串转为None
                'phone': user_info.get('phonenumber', '') or None,  # 空字符串转为None
                'avatar': user_info.get('avatar') or None,  # 空字符串转为None，避免URL验证错误
                'status': 1 if user_info.get('status', '0') == '0' else 0,  # Java: 0=正常, 1=停用
                'is_superuser': user_info.get('admin', False),
                'is_staff': True,
                'is_multi_login': True,  # Java系统默认支持多端登录
                'join_time': parse_java_time(user_info.get('createTime', '2021-01-01 00:00:00')),
                'last_login_time': parse_java_time(java_user.get('loginTime')) if java_user.get('loginTime') else None,

                # 部门信息
                'dept_id': user_info.get('deptId'),
                'dept': {
                    'id': user_info.get('deptId'),
                    'name': user_info.get('dept', {}).get('deptName', ''),
                    'parent_id': user_info.get('dept', {}).get('parentId'),
                    'sort': 0,
                    'leader': user_info.get('dept', {}).get('leader'),
                    'phone': None,
                    'email': None,
                    'status': 1,
                    'del_flag': False,
                    'created_time': parse_java_time(user_info.get('dept', {}).get('createTime', '2021-01-01 00:00:00')),
                    'updated_time': None,
                } if user_info.get('dept') else None,

                # 角色信息
                'roles': [
                    {
                        'id': role.get('roleId'),
                        'name': role.get('roleName', ''),
                        'status': 1 if role.get('status', '0') == '0' else 0,
                        'is_filter_scopes': True,
                        'remark': role.get('remark'),
                        'created_time': parse_java_time(role.get('createTime', '2021-01-01 00:00:00')),
                        'updated_time': None,
                        'menus': [],  # 暂时为空，如需要可以扩展
                        'scopes': []  # 暂时为空，如需要可以扩展
                    }
                    for role in user_info.get('roles', [])
                ],

                # 权限信息
                'permissions': java_user.get('permissions', []),
            }
            
            return GetUserInfoWithRelationDetail(**fba_user_data)
            
        except Exception as e:
            log.error(f"转换Java用户数据失败: {e}")
            raise errors.TokenError(msg='用户数据转换失败')


# 全局Java适配器实例
java_adapter = JavaAdapter()

217
backend/common/security/java_permission.py

普通文件
@ -0,0 +1,217 @@
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统权限装饰器和依赖注入
用于在路由级别进行权限控制
"""

from typing import Optional
from functools import wraps
from fastapi import Request, HTTPException, status, Depends

from backend.service.java_permission_service import java_permission_service
from backend.common.log import log


def require_java_permission(permission_code: str):
    """
    权限装饰器 - 要求特定权限
    
    :param permission_code: 权限代码 (如: system:user:list)
    :return: 装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                # 从kwargs中查找request
                request = kwargs.get('request')
            
            if not request:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="无法获取请求对象"
                )
            
            # 获取用户ID
            user_id = _get_user_id_from_request(request)
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户认证信息"
                )
            
            # 检查权限
            has_permission = await java_permission_service.check_user_permission(
                user_id, permission_code
            )
            
            if not has_permission:
                log.warning(f"权限检查失败: user_id={user_id}, permission={permission_code}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {permission_code}"
                )
            
            log.info(f"权限检查通过: user_id={user_id}, permission={permission_code}")
            
            # 权限检查通过，执行原函数
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_java_menu_access(menu_path: str):
    """
    菜单访问装饰器 - 要求特定菜单访问权限
    
    :param menu_path: 菜单路径 (如: /system/user)
    :return: 装饰器函数
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 从参数中获取request对象
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                request = kwargs.get('request')
            
            if not request:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="无法获取请求对象"
                )
            
            # 获取用户ID
            user_id = _get_user_id_from_request(request)
            if not user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户认证信息"
                )
            
            # 检查菜单访问权限
            has_access = await java_permission_service.check_user_menu_access(
                user_id, menu_path
            )
            
            if not has_access:
                log.warning(f"菜单访问权限检查失败: user_id={user_id}, menu_path={menu_path}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"无权访问菜单: {menu_path}"
                )
            
            log.info(f"菜单访问权限检查通过: user_id={user_id}, menu_path={menu_path}")
            
            # 权限检查通过，执行原函数
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


async def check_java_permission(request: Request, permission_code: str) -> bool:
    """
    检查Java系统权限 (依赖注入函数)
    
    :param request: HTTP请求
    :param permission_code: 权限代码
    :return: 是否有权限
    """
    try:
        user_id = _get_user_id_from_request(request)
        if not user_id:
            return False
        
        return await java_permission_service.check_user_permission(user_id, permission_code)
        
    except Exception as e:
        log.error(f"权限检查异常: {e}")
        return False


async def check_java_menu_access(request: Request, menu_path: str) -> bool:
    """
    检查Java系统菜单访问权限 (依赖注入函数)
    
    :param request: HTTP请求
    :param menu_path: 菜单路径
    :return: 是否可以访问
    """
    try:
        user_id = _get_user_id_from_request(request)
        if not user_id:
            return False
        
        return await java_permission_service.check_user_menu_access(user_id, menu_path)
        
    except Exception as e:
        log.error(f"菜单访问权限检查异常: {e}")
        return False


def _get_user_id_from_request(request: Request) -> Optional[int]:
    """
    从请求中获取用户ID
    
    :param request: HTTP请求
    :return: 用户ID
    """
    try:
        # 从认证中间件设置的用户信息中获取
        if hasattr(request.state, 'user_id'):
            return request.state.user_id
        
        # 从用户对象中获取
        if hasattr(request, 'user') and hasattr(request.user, 'id'):
            return request.user.id
        
        # 从Java认证信息中获取
        if hasattr(request.state, 'java_user') and 'userId' in request.state.java_user:
            return request.state.java_user['userId']
        
        return None
        
    except Exception as e:
        log.error(f"获取用户ID失败: {e}")
        return None


# 常用权限依赖注入
def RequireKnowledgeBaseList(request: Request):
    """知识库列表权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:list"))

def RequireKnowledgeBaseCreate(request: Request):
    """知识库创建权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:create"))

def RequireKnowledgeBaseUpdate(request: Request):
    """知识库更新权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:update"))

def RequireKnowledgeBaseDelete(request: Request):
    """知识库删除权限依赖"""
    return Depends(lambda: check_java_permission(request, "knowledge:base:delete"))

def RequireSystemUserList(request: Request):
    """系统用户列表权限依赖"""
    return Depends(lambda: check_java_permission(request, "system:user:list"))

def RequireSystemRoleList(request: Request):
    """系统角色列表权限依赖"""
    return Depends(lambda: check_java_permission(request, "system:role:list"))

66
backend/common/security/jwt.py

@ -21,6 +21,7 @@ from backend.app.admin.schema.user import GetUserInfoWithRelationDetail
from backend.common.dataclasses import AccessToken, NewToken, RefreshToken, TokenPayload
from backend.common.exception import errors
from backend.common.exception.errors import TokenError
from backend.common.security.java_adapter import java_adapter
from backend.core.conf import settings
from backend.database.db import async_db_session
from backend.database.redis import redis_client
@ -47,6 +48,8 @@ class CustomHTTPBearer(HTTPBearer):
# JWT authorizes dependency injection
DependsJwtAuth = Depends(CustomHTTPBearer())



password_hash = PasswordHash((BcryptHasher(),))


@ -273,32 +276,49 @@ def superuser_verify(request: Request) -> bool:

async def jwt_authentication(token: str) -> GetUserInfoWithRelationDetail:
    """
    JWT 认证
	    JWT 认证（支持 FBA 和 Java 系统）

    :param token: JWT token
    :return:
    """
    token_payload = jwt_decode(token)
    user_id = token_payload.id
    redis_token = await redis_client.get(f'{settings.TOKEN_REDIS_PREFIX}:{user_id}:{token_payload.session_uuid}')
    if not redis_token:
        raise errors.TokenError(msg='Token 已过期')
    # 首先尝试 FBA 系统的 JWT 认证
    try:
        token_payload = jwt_decode(token)
        user_id = token_payload.id
        redis_token = await redis_client.get(f'{settings.TOKEN_REDIS_PREFIX}:{user_id}:{token_payload.session_uuid}')
        if not redis_token:
            raise errors.TokenError(msg='Token 已过期')

        if token != redis_token:
            raise errors.TokenError(msg='Token 已失效')

        cache_user = await redis_client.get(f'{settings.JWT_USER_REDIS_PREFIX}:{user_id}')
        if not cache_user:
            async with async_db_session() as db:
                current_user = await get_current_user(db, user_id)
                user = GetUserInfoWithRelationDetail(**select_as_dict(current_user))
                await redis_client.setex(
                    f'{settings.JWT_USER_REDIS_PREFIX}:{user_id}',
                    settings.TOKEN_EXPIRE_SECONDS,
                    user.model_dump_json(),
                )
        else:
            # TODO: 在恰当的时机，应替换为使用 model_validate_json
            # https://docs.pydantic.dev/latest/concepts/json/#partial-json-parsing
            user = GetUserInfoWithRelationDetail.model_validate(from_json(cache_user, allow_partial=True))
        return user

    except errors.TokenError:
        # FBA token 验证失败，尝试 Java token 认证
        if java_adapter.is_enabled():
            try:
                return await java_adapter.authenticate_java_token(token)
            except errors.TokenError:
                # Java token 验证失败
                pass

        # 重新抛出原始的 token 错误
        raise errors.TokenError(msg='Token 无效或已过期')


    if token != redis_token:
        raise errors.TokenError(msg='Token 已失效')

    cache_user = await redis_client.get(f'{settings.JWT_USER_REDIS_PREFIX}:{user_id}')
    if not cache_user:
        async with async_db_session() as db:
            current_user = await get_current_user(db, user_id)
            user = GetUserInfoWithRelationDetail(**select_as_dict(current_user))
            await redis_client.setex(
                f'{settings.JWT_USER_REDIS_PREFIX}:{user_id}',
                settings.TOKEN_EXPIRE_SECONDS,
                user.model_dump_json(),
            )
    else:
        # TODO: 在恰当的时机，应替换为使用 model_validate_json
        # https://docs.pydantic.dev/latest/concepts/json/#partial-json-parsing
        user = GetUserInfoWithRelationDetail.model_validate(from_json(cache_user, allow_partial=True))
    return user


144
backend/database/java_db.py

普通文件
@ -0,0 +1,144 @@
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统数据库连接配置
用于连接Java系统的MySQL数据库，查询用户权限等信息
"""

import sys
from typing import AsyncGenerator

from sqlalchemy import URL, text
from sqlalchemy.ext.asyncio import AsyncEngine, AsyncSession, async_sessionmaker, create_async_engine

from backend.common.log import log


class JavaDatabaseConfig:
    """Java系统数据库配置"""
    
    # Java系统MySQL数据库连接信息
    HOST = "*************"
    PORT = 5981
    DATABASE = "fastbee5"
    USERNAME = "root"
    PASSWORD = "123456"
    CHARSET = "utf8mb4"


def create_java_database_url() -> URL:
    """
    创建Java系统数据库连接URL
    
    :return: 数据库连接URL
    """
    url = URL.create(
        drivername='mysql+asyncmy',
        username=JavaDatabaseConfig.USERNAME,
        password=JavaDatabaseConfig.PASSWORD,
        host=JavaDatabaseConfig.HOST,
        port=JavaDatabaseConfig.PORT,
        database=JavaDatabaseConfig.DATABASE,
    )
    url = url.update_query_dict({'charset': JavaDatabaseConfig.CHARSET})
    return url


def create_java_async_engine_and_session() -> tuple[AsyncEngine, async_sessionmaker[AsyncSession]]:
    """
    创建Java系统数据库引擎和Session
    
    :return: (engine, session_maker)
    """
    try:
        url = create_java_database_url()
        
        # 数据库引擎
        engine = create_async_engine(
            url,
            echo=False,  # 不输出SQL日志，避免干扰
            echo_pool=False,
            future=True,
            # 较小的连接池，因为主要用于权限查询
            pool_size=5,
            max_overflow=10,
            pool_timeout=30,
            pool_recycle=3600,
            pool_pre_ping=True,
            pool_use_lifo=False,
        )
        
        log.info("✅ Java系统数据库连接创建成功")
        
    except Exception as e:
        log.error(f'❌ Java系统数据库连接失败: {e}')
        raise
    else:
        db_session = async_sessionmaker(
            bind=engine,
            class_=AsyncSession,
            autoflush=False,
            expire_on_commit=False,
        )
        return engine, db_session


# 创建Java系统数据库连接
java_engine, java_async_db_session = create_java_async_engine_and_session()


async def get_java_db() -> AsyncGenerator[AsyncSession, None]:
    """
    获取Java系统数据库会话
    
    :return: AsyncSession
    """
    async with java_async_db_session() as session:
        try:
            yield session
        except Exception as e:
            await session.rollback()
            log.error(f"Java数据库会话错误: {e}")
            raise
        finally:
            await session.close()


async def test_java_db_connection() -> bool:
    """
    测试Java系统数据库连接
    
    :return: 连接是否成功
    """
    try:
        async with java_async_db_session() as session:
            # 执行简单查询测试连接
            result = await session.execute(text("SELECT 1 as test"))
            test_value = result.scalar()
            
            if test_value == 1:
                log.info("✅ Java系统数据库连接测试成功")
                return True
            else:
                log.error("❌ Java系统数据库连接测试失败: 返回值异常")
                return False
                
    except Exception as e:
        log.error(f"❌ Java系统数据库连接测试失败: {e}")
        return False


if __name__ == "__main__":
    import asyncio
    
    async def main():
        """测试连接"""
        print("🔧 测试Java系统数据库连接...")
        success = await test_java_db_connection()
        if success:
            print("🎉 Java系统数据库连接成功!")
        else:
            print("❌ Java系统数据库连接失败!")
            sys.exit(1)
    
    asyncio.run(main())

221
backend/middleware/java_permission_middleware.py

普通文件
@ -0,0 +1,221 @@
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统权限控制中间件
集成Java系统的权限查询服务到FastAPI权限控制中
"""

from typing import Callable, Optional
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

from backend.service.java_permission_service import java_permission_service
from backend.common.log import log


class JavaPermissionMiddleware(BaseHTTPMiddleware):
    """Java系统权限控制中间件"""
    
    def __init__(self, app, exclude_paths: Optional[list] = None):
        """
        初始化权限中间件
        
        :param app: FastAPI应用实例
        :param exclude_paths: 排除权限检查的路径列表
        """
        super().__init__(app)
        
        # 默认排除的路径
        self.exclude_paths = exclude_paths or [
            "/docs",
            "/redoc", 
            "/openapi.json",
            "/api/v1/auth/login",
            "/api/iot/v1/knowledge-base/health",  # 健康检查接口
        ]
        
        # 权限路径映射 (API路径 -> 权限代码)
        self.permission_mapping = {
            # 知识库相关权限
            "/api/iot/v1/knowledge-base/list": "knowledge:base:list",
            "/api/iot/v1/knowledge-base/create": "knowledge:base:create", 
            "/api/iot/v1/knowledge-base/update": "knowledge:base:update",
            "/api/iot/v1/knowledge-base/delete": "knowledge:base:delete",
            "/api/iot/v1/knowledge-base/stats": "knowledge:base:stats",
            
            # 系统管理权限
            "/api/v1/sys/users": "system:user:list",
            "/api/v1/sys/roles": "system:role:list",
            "/api/v1/sys/menus": "system:menu:list",
        }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """
        权限检查中间件处理逻辑
        
        :param request: HTTP请求
        :param call_next: 下一个中间件或路由处理器
        :return: HTTP响应
        """
        try:
            # 检查是否需要权限验证
            if not self._need_permission_check(request):
                return await call_next(request)
            
            # 获取用户ID (从认证中间件设置的用户信息中获取)
            user_id = self._get_user_id_from_request(request)
            if not user_id:
                log.warning(f"权限检查失败: 未找到用户ID, path={request.url.path}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="未找到用户认证信息"
                )
            
            # 获取所需权限
            required_permission = self._get_required_permission(request)
            if not required_permission:
                # 没有配置权限要求，允许通过
                return await call_next(request)
            
            # 检查用户权限
            has_permission = await java_permission_service.check_user_permission(
                user_id, required_permission
            )
            
            if not has_permission:
                log.warning(f"权限检查失败: user_id={user_id}, permission={required_permission}, path={request.url.path}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {required_permission}"
                )
            
            log.info(f"权限检查通过: user_id={user_id}, permission={required_permission}, path={request.url.path}")
            
            # 权限检查通过，继续处理请求
            return await call_next(request)
            
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            log.error(f"权限中间件处理异常: {e}, path={request.url.path}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="权限检查服务异常"
            )
    
    def _need_permission_check(self, request: Request) -> bool:
        """
        判断是否需要权限检查
        
        :param request: HTTP请求
        :return: 是否需要权限检查
        """
        path = request.url.path
        
        # 检查排除路径
        for exclude_path in self.exclude_paths:
            if path.startswith(exclude_path):
                return False
        
        # 只对API路径进行权限检查
        if not path.startswith("/api/"):
            return False
        
        return True
    
    def _get_user_id_from_request(self, request: Request) -> Optional[int]:
        """
        从请求中获取用户ID
        
        :param request: HTTP请求
        :return: 用户ID
        """
        try:
            # 从认证中间件设置的用户信息中获取
            if hasattr(request.state, 'user_id'):
                return request.state.user_id
            
            # 从用户对象中获取 (如果有的话)
            if hasattr(request, 'user') and hasattr(request.user, 'id'):
                return request.user.id
            
            # 从Java认证信息中获取
            if hasattr(request.state, 'java_user') and 'userId' in request.state.java_user:
                return request.state.java_user['userId']
            
            return None
            
        except Exception as e:
            log.error(f"获取用户ID失败: {e}")
            return None
    
    def _get_required_permission(self, request: Request) -> Optional[str]:
        """
        获取请求所需的权限代码
        
        :param request: HTTP请求
        :return: 权限代码
        """
        path = request.url.path
        method = request.method
        
        # 精确匹配
        if path in self.permission_mapping:
            return self.permission_mapping[path]
        
        # 模糊匹配 (根据路径模式)
        if path.startswith("/api/iot/v1/knowledge-base/"):
            if method == "GET":
                return "knowledge:base:query"
            elif method == "POST":
                return "knowledge:base:create"
            elif method in ["PUT", "PATCH"]:
                return "knowledge:base:update"
            elif method == "DELETE":
                return "knowledge:base:delete"
        
        # 系统管理相关
        if path.startswith("/api/v1/sys/"):
            if "user" in path:
                return "system:user:query"
            elif "role" in path:
                return "system:role:query"
            elif "menu" in path:
                return "system:menu:query"
        
        # 默认返回None，表示不需要特殊权限
        return None
    
    def add_permission_mapping(self, path: str, permission: str):
        """
        添加权限映射
        
        :param path: API路径
        :param permission: 权限代码
        """
        self.permission_mapping[path] = permission
    
    def remove_permission_mapping(self, path: str):
        """
        移除权限映射
        
        :param path: API路径
        """
        if path in self.permission_mapping:
            del self.permission_mapping[path]


# 创建权限中间件实例
def create_java_permission_middleware(exclude_paths: Optional[list] = None):
    """
    创建Java权限中间件实例
    
    :param exclude_paths: 排除权限检查的路径列表
    :return: 中间件类
    """
    def middleware_factory(app):
        return JavaPermissionMiddleware(app, exclude_paths)
    
    return middleware_factory

285
backend/service/java_permission_service.py

普通文件
@ -0,0 +1,285 @@
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Java系统权限查询服务
从Java系统的MySQL数据库中查询用户权限信息
"""

from typing import List, Dict, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from backend.database.java_db import get_java_db
from backend.common.log import log


class JavaPermissionService:
    """Java系统权限查询服务"""

    @staticmethod
    def _is_admin_user(user_id: int) -> bool:
        """
        判断是否为超级管理员用户 (Java系统逻辑)

        :param user_id: 用户ID
        :return: 是否为超级管理员
        """
        # Java系统逻辑: SysUser.isAdmin(userId) -> userId != null && 1L == userId
        return user_id == 1

    @staticmethod
    def _is_admin_role(role_id: int) -> bool:
        """
        判断是否为超级管理员角色 (Java系统逻辑)

        :param role_id: 角色ID
        :return: 是否为超级管理员角色
        """
        # Java系统逻辑: SysRole.isAdmin(roleId) -> roleId != null && 1L == roleId
        return role_id == 1
    
    @staticmethod
    async def get_user_permissions(user_id: int) -> Dict:
        """
        获取用户的完整权限信息

        :param user_id: 用户ID
        :return: 用户权限信息字典
        """
        try:
            async for db in get_java_db():
                # 1. 获取用户基本信息
                user_info = await JavaPermissionService._get_user_info(db, user_id)
                if not user_info:
                    return {"error": "用户不存在"}

                # 2. 检查是否为超级管理员 (Java系统逻辑: userId == 1)
                is_admin = JavaPermissionService._is_admin_user(user_id)

                # 3. 获取用户角色
                user_roles = await JavaPermissionService._get_user_roles(db, user_id)

                # 4. 获取用户权限
                if is_admin:
                    # 超级管理员直接赋予所有权限 (Java系统逻辑)
                    user_permissions = [{
                        "menu_id": 0,
                        "menu_name": "超级管理员权限",
                        "parent_id": 0,
                        "path": "*",
                        "component": "*",
                        "perms": "*:*:*",
                        "menu_type": "F",
                        "visible": "0",
                        "status": "0",
                        "order_num": 0,
                    }]
                    permission_codes = ["*:*:*"]
                    menu_paths = ["*"]
                else:
                    user_permissions = await JavaPermissionService._get_user_permissions_by_roles(db, user_roles)
                    permission_codes = [p["perms"] for p in user_permissions if p["perms"]]
                    menu_paths = [p["path"] for p in user_permissions if p["path"]]

                # 5. 组装完整权限信息
                permission_info = {
                    "user_id": user_info["user_id"],
                    "username": user_info["user_name"],
                    "nickname": user_info["nick_name"],
                    "email": user_info["email"],
                    "status": user_info["status"],
                    "dept_id": user_info["dept_id"],
                    "is_admin": is_admin,
                    "roles": user_roles,
                    "permissions": user_permissions,
                    "permission_codes": permission_codes,
                    "menu_paths": menu_paths,
                }

                log.info(f"获取用户权限成功: user_id={user_id}, is_admin={is_admin}, permissions={len(user_permissions)}")
                return permission_info
                
        except Exception as e:
            log.error(f"获取用户权限失败: user_id={user_id}, error={e}")
            return {"error": str(e)}
    
    @staticmethod
    async def _get_user_info(db: AsyncSession, user_id: int) -> Optional[Dict]:
        """获取用户基本信息"""
        try:
            query = text("""
                SELECT user_id, user_name, nick_name, email, status, dept_id, del_flag
                FROM sys_user 
                WHERE user_id = :user_id AND del_flag = '0'
            """)
            
            result = await db.execute(query, {"user_id": user_id})
            row = result.fetchone()
            
            if row:
                return {
                    "user_id": row[0],
                    "user_name": row[1],
                    "nick_name": row[2],
                    "email": row[3],
                    "status": row[4],
                    "dept_id": row[5],
                }
            return None
            
        except Exception as e:
            log.error(f"获取用户信息失败: user_id={user_id}, error={e}")
            return None
    
    @staticmethod
    async def _get_user_roles(db: AsyncSession, user_id: int) -> List[Dict]:
        """获取用户角色列表"""
        try:
            query = text("""
                SELECT r.role_id, r.role_name, r.role_key, r.status
                FROM sys_role r
                INNER JOIN sys_user_role ur ON r.role_id = ur.role_id
                WHERE ur.user_id = :user_id AND r.del_flag = '0' AND r.status = '0'
            """)
            
            result = await db.execute(query, {"user_id": user_id})
            rows = result.fetchall()
            
            roles = []
            for row in rows:
                roles.append({
                    "role_id": row[0],
                    "role_name": row[1],
                    "role_key": row[2],
                    "status": row[3],
                })
            
            return roles
            
        except Exception as e:
            log.error(f"获取用户角色失败: user_id={user_id}, error={e}")
            return []
    
    @staticmethod
    async def _get_user_permissions_by_roles(db: AsyncSession, roles: List[Dict]) -> List[Dict]:
        """根据角色获取权限列表"""
        try:
            if not roles:
                return []
            
            role_ids = [role["role_id"] for role in roles]
            role_ids_str = ",".join(map(str, role_ids))
            
            query = text(f"""
                SELECT DISTINCT m.menu_id, m.menu_name, m.parent_id, m.path,
                       m.component, m.perms, m.menu_type, m.visible, m.status, m.order_num
                FROM sys_menu m
                INNER JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
                WHERE rm.role_id IN ({role_ids_str})
                  AND m.status = '0'
                ORDER BY m.order_num, m.menu_id
            """)
            
            result = await db.execute(query)
            rows = result.fetchall()
            
            permissions = []
            for row in rows:
                permissions.append({
                    "menu_id": row[0],
                    "menu_name": row[1],
                    "parent_id": row[2],
                    "path": row[3],
                    "component": row[4],
                    "perms": row[5],
                    "menu_type": row[6],
                    "visible": row[7],
                    "status": row[8],
                    "order_num": row[9],
                })
            
            return permissions
            
        except Exception as e:
            log.error(f"获取角色权限失败: roles={roles}, error={e}")
            return []
    
    @staticmethod
    async def check_user_permission(user_id: int, permission_code: str) -> bool:
        """
        检查用户是否有特定权限
        
        :param user_id: 用户ID
        :param permission_code: 权限代码 (如: system:user:list)
        :return: 是否有权限
        """
        try:
            permission_info = await JavaPermissionService.get_user_permissions(user_id)
            
            if "error" in permission_info:
                return False
            
            # 检查权限代码
            permission_codes = permission_info.get("permission_codes", [])
            
            # 支持通配符权限 (如: *:*:*)
            if "*:*:*" in permission_codes:
                return True
            
            # 精确匹配
            if permission_code in permission_codes:
                return True
            
            # 模糊匹配 (如: system:user:* 匹配 system:user:list)
            for code in permission_codes:
                if code.endswith("*"):
                    prefix = code[:-1]
                    if permission_code.startswith(prefix):
                        return True
            
            return False
            
        except Exception as e:
            log.error(f"检查用户权限失败: user_id={user_id}, permission={permission_code}, error={e}")
            return False
    
    @staticmethod
    async def check_user_menu_access(user_id: int, menu_path: str) -> bool:
        """
        检查用户是否可以访问特定菜单路径

        :param user_id: 用户ID
        :param menu_path: 菜单路径 (如: /system/user)
        :return: 是否可以访问
        """
        try:
            permission_info = await JavaPermissionService.get_user_permissions(user_id)

            if "error" in permission_info:
                return False

            # 超级管理员可以访问所有菜单
            if permission_info.get("is_admin", False):
                return True

            # 检查菜单路径
            menu_paths = permission_info.get("menu_paths", [])

            # 精确匹配
            if menu_path in menu_paths:
                return True

            # 检查父路径权限
            for path in menu_paths:
                if path and menu_path.startswith(path):
                    return True

            return False

        except Exception as e:
            log.error(f"检查菜单访问权限失败: user_id={user_id}, menu_path={menu_path}, error={e}")
            return False


# 创建服务实例
java_permission_service = JavaPermissionService()

35
backend/start_stable.py

普通文件
@ -0,0 +1,35 @@
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
稳定的FastAPI启动脚本
不使用文件监控，避免自动重启问题
"""
import sys
import traceback
import uvicorn

def main():
    try:
        print("正在导入FastAPI应用...")
        from backend.main import app
        print("✅ FastAPI应用导入成功")

        print("正在启动服务器...")
        # 生产模式启动，不监控文件变化
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8000,
            reload=False,  # 关闭自动重载
            log_level="info",
            access_log=True,
            workers=1  # 单进程模式，便于调试
        )
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        print("详细错误信息:")
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()

292
docs/java_permission_adapter.md

普通文件
@ -0,0 +1,292 @@
# Java权限适配器系统文档

## 概述

Java权限适配器系统是为了实现FastAPI应用与现有Java权限系统的无缝集成而设计的。该系统允许FastAPI应用直接使用Java系统的JWT token进行用户认证，并复用Java系统的权限控制机制。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Java系统      │    │  FastAPI应用    │    │   Redis缓存     │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ JWT Token   │ │───▶│ │Java Adapter │ │───▶│ │ 用户信息    │ │
│ │ 生成        │ │    │ │             │ │    │ │ 缓存        │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │        │        │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │ 权限数据    │ │    │ │ 权限中间件  │ │    │                 │
│ │ 存储        │ │    │ │             │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 核心组件

### 1. JavaAdapter (java_adapter.py)

**功能**: 处理Java系统JWT token的认证和用户数据转换

**主要方法**:
- `authenticate_java_token(token: str)`: 认证Java JWT token
- `_parse_java_json(json_str: str)`: 解析Java特有的JSON格式
- `_convert_java_user_to_fba(java_user: Dict)`: 转换Java用户数据为FBA格式

**特性**:
	- 支持Java JWT token的多种格式（标准JWT和兼容性解析）
	- 处理Java特有的集合序列化格式（Set[], List[], HashSet[]等）
- 自动转换Java用户数据结构为FastAPI应用格式

### 2. Java权限装饰器 (java_permission.py)

**功能**: 提供路由级别的权限控制装饰器和依赖注入

**主要装饰器**:
- `@require_java_permission(permission_code)`: 要求特定权限
- `@require_java_menu_access(menu_path)`: 要求菜单访问权限

**依赖注入函数**:
- `check_java_permission(request, permission_code)`: 检查权限
- `check_java_menu_access(request, menu_path)`: 检查菜单访问权限

### 3. Java权限中间件 (java_permission_middleware.py)

**功能**: 全局请求拦截，处理Java token认证

**处理流程**:
1. 检测请求头中的Authorization token
2. 判断是否为Java系统token
3. 调用JavaAdapter进行认证
4. 将用户信息存储到request.state中

### 4. Java权限服务 (java_permission_service.py)

**功能**: 提供权限检查的业务逻辑

**主要方法**:
- `check_user_permission(user_id, permission_code)`: 检查用户权限
- `check_user_menu_access(user_id, menu_path)`: 检查菜单访问权限
- `get_user_permissions(user_id)`: 获取用户所有权限

### 5. Java数据库适配器 (java_db.py)

**功能**: 连接Java系统数据库，查询权限相关数据

## 使用方法

### 1. 在路由中使用权限装饰器

```python
from backend.common.security.java_permission import require_java_permission

@router.get("/users")
@require_java_permission("system:user:list")
async def get_users():
    # 只有拥有 system:user:list 权限的用户才能访问
    return {"users": []}
```

### 2. 使用依赖注入方式

```python
from fastapi import Depends
from backend.common.security.java_permission import check_java_permission

@router.get("/users")
async def get_users(
    request: Request,
    has_permission: bool = Depends(
        lambda req: check_java_permission(req, "system:user:list")
    )
):
    if not has_permission:
        raise HTTPException(status_code=403, detail="权限不足")
    return {"users": []}
```

### 3. 使用预定义的权限依赖

```python
from backend.common.security.java_permission import RequireKnowledgeBaseList

@router.get("/knowledge-bases")
async def get_knowledge_bases(
    request: Request,
    _: bool = RequireKnowledgeBaseList(request)
):
    return {"knowledge_bases": []}
```

## 配置说明

### 1. JWT密钥配置

	确保FastAPI应用的JWT密钥与Java系统保持一致：

```python
# backend/core/conf.py
TOKEN_SECRET_KEY = "your-java-jwt-secret-key"
```

### 2. Redis配置

	Java系统的用户信息缓存在Redis中，键格式为：`login_tokens:{uuid}`

### 3. 数据库连接

	配置Java系统数据库连接，用于权限数据查询：

```python
# backend/database/java_db.py
JAVA_DATABASE_URL = "mysql://user:password@host:port/database"
```

## 权限代码规范

### 权限代码格式

权限代码采用三级结构：`模块:功能:操作`

**示例**:
- `system:user:list` - 系统用户列表查看权限
- `system:user:create` - 系统用户创建权限
- `knowledge:base:update` - 知识库更新权限
- `knowledge:base:delete` - 知识库删除权限

### 菜单路径格式

	菜单路径采用URL路径格式：`/模块/子模块`

**示例**:
- `/system/user` - 系统用户管理菜单
- `/system/role` - 系统角色管理菜单
- `/knowledge/base` - 知识库管理菜单

## 错误处理

### 常见错误类型

1. **TokenError**: Token相关错误
   - Token格式错误
   - Token已过期
   - Token认证失败

2. **PermissionError**: 权限相关错误
   - 权限不足
   - 菜单访问被拒绝

3. **DatabaseError**: 数据库相关错误
   - 连接失败
   - 查询异常

### 错误响应格式

```json
{
    "detail": "权限不足，需要权限: system:user:list",
    "status_code": 403
}
```

## 日志记录

系统会记录以下关键操作：

- JWT token认证成功/失败
- 权限检查通过/失败
- 用户数据转换异常
- 数据库连接异常

**日志级别**:
- INFO: 正常操作记录
- WARNING: 权限检查失败
- ERROR: 系统异常

## 性能优化

### 1. 缓存策略

- 用户权限信息缓存30分钟
- 菜单访问权限缓存1小时
- Redis连接池复用

### 2. 数据库优化

- 权限查询使用索引
- 批量权限检查
- 连接池管理

## 安全考虑

### 1. Token安全

- JWT签名验证
- Token过期时间检查
- 防止Token重放攻击

### 2. 权限安全

- 最小权限原则
- 权限检查日志记录
- 敏感操作二次验证

## 故障排查

### 1. Token认证失败

**检查项**:
- JWT密钥是否一致
- Token格式是否正确
- Redis中是否存在用户数据
- Token是否已过期

### 2. 权限检查失败

**检查项**:
- 用户是否拥有对应权限
- 权限代码是否正确
- 数据库连接是否正常
- 权限数据是否同步

### 3. 性能问题

**检查项**:
- Redis连接是否正常
- 数据库查询是否有索引
- 缓存命中率是否正常
- 并发请求数量

## 扩展指南

### 1. 添加新的权限类型

1. 在`java_permission.py`中添加新的装饰器
2. 在`java_permission_service.py`中实现检查逻辑
3. 更新权限代码规范文档

### 2. 集成其他认证系统

1. 创建新的适配器类
2. 实现统一的认证接口
3. 更新中间件支持多种认证方式

### 3. 添加权限缓存

1. 扩展Redis缓存策略
2. 实现权限数据预加载
3. 添加缓存失效机制

## 版本历史

- v1.0.0: 初始版本，支持基本的Java token认证和权限检查
- v1.1.0: 添加菜单访问权限控制
- v1.2.0: 优化性能，添加缓存机制
- v1.3.0: 增强错误处理和日志记录

## 联系方式

如有问题或建议，请联系开发团队：
- 技术负责人: [技术负责人邮箱]
- 开发团队: [开发团队邮箱]
- 项目地址: https://git.978543210.com/IOT-RD/fastapi_best_architecture.git
Powered by Gitea
当前版本: 1.22.6 页面:
1177ms
模板:
1094ms